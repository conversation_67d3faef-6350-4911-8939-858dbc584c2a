import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../models/chapter_snapshot.dart';

/// 版本控制服务
class VersionControlService extends GetxService {
  static const String _historyBoxName = 'version_control_history';
  static const String _snapshotBoxName = 'chapter_snapshots';
  
  late Box<VersionControlHistory> _historyBox;
  late Box<ChapterSnapshot> _snapshotBox;
  
  // 撤销/重做栈
  final List<VersionControlHistory> _undoStack = [];
  final List<VersionControlHistory> _redoStack = [];
  
  // 当前操作状态
  final RxBool canUndo = false.obs;
  final RxBool canRedo = false.obs;
  
  final _uuid = const Uuid();

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeBoxes();
  }

  /// 初始化Hive存储盒子
  Future<void> _initializeBoxes() async {
    try {
      _historyBox = await Hive.openBox<VersionControlHistory>(_historyBoxName);
      _snapshotBox = await Hive.openBox<ChapterSnapshot>(_snapshotBoxName);
      print('[VersionControlService] 存储盒子初始化成功');
    } catch (e) {
      print('[VersionControlService] 初始化失败: $e');
    }
  }

  /// 记录版本控制历史
  Future<void> recordChange({
    required String novelId,
    required int chapterNumber,
    required String previousTitle,
    required String currentTitle,
    required String previousContent,
    required String currentContent,
    required VersionControlAction action,
  }) async {
    try {
      final history = VersionControlHistory(
        id: _uuid.v4(),
        chapterNumber: chapterNumber,
        previousTitle: previousTitle,
        currentTitle: currentTitle,
        previousContent: previousContent,
        currentContent: currentContent,
        timestamp: DateTime.now(),
        action: action,
        novelId: novelId,
      );

      // 添加到撤销栈
      _undoStack.add(history);
      
      // 清空重做栈（新操作后不能重做之前的撤销）
      _redoStack.clear();
      
      // 限制撤销栈大小（保留最近50个操作）
      if (_undoStack.length > 50) {
        _undoStack.removeAt(0);
      }

      // 保存到持久化存储
      await _historyBox.put(history.id, history);
      
      _updateButtonStates();
      
      print('[VersionControlService] 记录变更: ${action.name}');
    } catch (e) {
      print('[VersionControlService] 记录变更失败: $e');
    }
  }

  /// 撤销操作
  VersionControlHistory? undo() {
    if (_undoStack.isEmpty) return null;
    
    final lastChange = _undoStack.removeLast();
    _redoStack.add(lastChange);
    
    _updateButtonStates();
    
    print('[VersionControlService] 撤销操作: ${lastChange.action.name}');
    return lastChange;
  }

  /// 重做操作
  VersionControlHistory? redo() {
    if (_redoStack.isEmpty) return null;
    
    final redoChange = _redoStack.removeLast();
    _undoStack.add(redoChange);
    
    _updateButtonStates();
    
    print('[VersionControlService] 重做操作: ${redoChange.action.name}');
    return redoChange;
  }

  /// 创建快照
  Future<ChapterSnapshot> createSnapshot({
    required String novelId,
    required int chapterNumber,
    required String title,
    required String content,
    required String userNote,
  }) async {
    try {
      final snapshot = ChapterSnapshot(
        id: _uuid.v4(),
        chapterNumber: chapterNumber,
        title: title,
        content: content,
        createdAt: DateTime.now(),
        userNote: userNote,
        novelId: novelId,
      );

      await _snapshotBox.put(snapshot.id, snapshot);
      
      print('[VersionControlService] 创建快照: ${snapshot.userNote}');
      return snapshot;
    } catch (e) {
      print('[VersionControlService] 创建快照失败: $e');
      rethrow;
    }
  }

  /// 获取章节的所有快照
  List<ChapterSnapshot> getChapterSnapshots(String novelId, int chapterNumber) {
    try {
      return _snapshotBox.values
          .where((snapshot) => 
              snapshot.novelId == novelId && 
              snapshot.chapterNumber == chapterNumber)
          .toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt)); // 按时间倒序
    } catch (e) {
      print('[VersionControlService] 获取快照失败: $e');
      return [];
    }
  }

  /// 删除快照
  Future<void> deleteSnapshot(String snapshotId) async {
    try {
      await _snapshotBox.delete(snapshotId);
      print('[VersionControlService] 删除快照: $snapshotId');
    } catch (e) {
      print('[VersionControlService] 删除快照失败: $e');
    }
  }

  /// 清理指定小说的所有数据
  Future<void> clearNovelData(String novelId) async {
    try {
      // 清理历史记录
      final historyKeys = _historyBox.keys.where((key) {
        final history = _historyBox.get(key);
        return history?.novelId == novelId;
      }).toList();
      
      for (final key in historyKeys) {
        await _historyBox.delete(key);
      }

      // 清理快照
      final snapshotKeys = _snapshotBox.keys.where((key) {
        final snapshot = _snapshotBox.get(key);
        return snapshot?.novelId == novelId;
      }).toList();
      
      for (final key in snapshotKeys) {
        await _snapshotBox.delete(key);
      }

      // 清理内存中的栈
      _undoStack.removeWhere((history) => history.novelId == novelId);
      _redoStack.removeWhere((history) => history.novelId == novelId);
      
      _updateButtonStates();
      
      print('[VersionControlService] 清理小说数据: $novelId');
    } catch (e) {
      print('[VersionControlService] 清理数据失败: $e');
    }
  }

  /// 更新按钮状态
  void _updateButtonStates() {
    canUndo.value = _undoStack.isNotEmpty;
    canRedo.value = _redoStack.isNotEmpty;
  }

  /// 加载指定小说的历史记录到内存
  Future<void> loadNovelHistory(String novelId) async {
    try {
      _undoStack.clear();
      _redoStack.clear();

      final histories = _historyBox.values
          .where((history) => history.novelId == novelId)
          .toList()
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

      _undoStack.addAll(histories);
      _updateButtonStates();
      
      print('[VersionControlService] 加载小说历史记录: ${histories.length}条');
    } catch (e) {
      print('[VersionControlService] 加载历史记录失败: $e');
    }
  }
}
