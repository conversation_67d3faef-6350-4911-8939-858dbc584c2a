import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../models/chapter_snapshot.dart';
import '../models/novel.dart';

/// 版本控制服务
class VersionControlService extends GetxService {
  static const String _snapshotsBoxName = 'chapter_snapshots';
  static const String _operationsBoxName = 'edit_operations';
  
  late Box<ChapterSnapshot> _snapshotsBox;
  late Box<EditOperation> _operationsBox;
  
  final Uuid _uuid = const Uuid();
  
  // 撤销/重做栈
  final List<EditOperation> _undoStack = [];
  final List<EditOperation> _redoStack = [];
  
  // 当前操作状态
  final RxBool canUndo = false.obs;
  final RxBool canRedo = false.obs;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeBoxes();
  }
  
  Future<void> _initializeBoxes() async {
    try {
      _snapshotsBox = await Hive.openBox<ChapterSnapshot>(_snapshotsBoxName);
      _operationsBox = await Hive.openBox<EditOperation>(_operationsBoxName);
      print('[VersionControl] 版本控制服务初始化完成');
    } catch (e) {
      print('[VersionControl] 初始化失败: $e');
    }
  }
  
  /// 创建章节快照
  Future<String> createSnapshot({
    required String novelId,
    required Chapter chapter,
    required String description,
  }) async {
    try {
      final snapshot = ChapterSnapshot(
        id: _uuid.v4(),
        chapterNumber: chapter.number,
        title: chapter.title,
        content: chapter.content,
        createdAt: DateTime.now(),
        description: description,
        novelId: novelId,
      );
      
      await _snapshotsBox.put(snapshot.id, snapshot);
      print('[VersionControl] 快照创建成功: ${snapshot.id}');
      return snapshot.id;
    } catch (e) {
      print('[VersionControl] 创建快照失败: $e');
      rethrow;
    }
  }
  
  /// 获取章节的所有快照
  List<ChapterSnapshot> getChapterSnapshots(String novelId, int chapterNumber) {
    try {
      final snapshots = _snapshotsBox.values
          .where((snapshot) => 
              snapshot.novelId == novelId && 
              snapshot.chapterNumber == chapterNumber)
          .toList();
      
      // 按创建时间倒序排列
      snapshots.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return snapshots;
    } catch (e) {
      print('[VersionControl] 获取快照失败: $e');
      return [];
    }
  }
  
  /// 删除快照
  Future<void> deleteSnapshot(String snapshotId) async {
    try {
      await _snapshotsBox.delete(snapshotId);
      print('[VersionControl] 快照删除成功: $snapshotId');
    } catch (e) {
      print('[VersionControl] 删除快照失败: $e');
    }
  }
  
  /// 记录编辑操作
  Future<void> recordEditOperation({
    required int chapterNumber,
    required String beforeTitle,
    required String afterTitle,
    required String beforeContent,
    required String afterContent,
    required String operationType,
  }) async {
    try {
      // 如果内容没有变化，不记录操作
      if (beforeTitle == afterTitle && beforeContent == afterContent) {
        return;
      }
      
      final operation = EditOperation(
        id: _uuid.v4(),
        chapterNumber: chapterNumber,
        beforeTitle: beforeTitle,
        afterTitle: afterTitle,
        beforeContent: beforeContent,
        afterContent: afterContent,
        timestamp: DateTime.now(),
        operationType: operationType,
      );
      
      // 添加到撤销栈
      _undoStack.add(operation);
      
      // 清空重做栈（新操作会使重做无效）
      _redoStack.clear();
      
      // 限制撤销栈大小（保留最近50个操作）
      if (_undoStack.length > 50) {
        _undoStack.removeAt(0);
      }
      
      // 保存到数据库
      await _operationsBox.put(operation.id, operation);
      
      // 更新状态
      _updateUndoRedoState();
      
      print('[VersionControl] 编辑操作记录成功: ${operation.operationType}');
    } catch (e) {
      print('[VersionControl] 记录编辑操作失败: $e');
    }
  }
  
  /// 撤销操作
  EditOperation? undo() {
    if (_undoStack.isEmpty) return null;
    
    final operation = _undoStack.removeLast();
    _redoStack.add(operation);
    
    _updateUndoRedoState();
    
    print('[VersionControl] 撤销操作: ${operation.operationType}');
    return operation;
  }
  
  /// 重做操作
  EditOperation? redo() {
    if (_redoStack.isEmpty) return null;
    
    final operation = _redoStack.removeLast();
    _undoStack.add(operation);
    
    _updateUndoRedoState();
    
    print('[VersionControl] 重做操作: ${operation.operationType}');
    return operation;
  }
  
  /// 更新撤销/重做状态
  void _updateUndoRedoState() {
    canUndo.value = _undoStack.isNotEmpty;
    canRedo.value = _redoStack.isNotEmpty;
  }
  
  /// 清空操作历史
  void clearHistory() {
    _undoStack.clear();
    _redoStack.clear();
    _updateUndoRedoState();
  }
  
  /// 获取操作历史统计
  Map<String, int> getHistoryStats() {
    return {
      'undoCount': _undoStack.length,
      'redoCount': _redoStack.length,
      'totalSnapshots': _snapshotsBox.length,
      'totalOperations': _operationsBox.length,
    };
  }
}
