﻿import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/novel_agent_controller.dart';
import '../controllers/novel_controller.dart';
import '../models/novel.dart';
import '../widgets/novel_agent_panel.dart';

class DaizongAIAssistantScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const DaizongAIAssistantScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<DaizongAIAssistantScreen> createState() => _DaizongAIAssistantScreenState();
}

class _DaizongAIAssistantScreenState extends State<DaizongAIAssistantScreen> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  final NovelController _novelController = Get.find<NovelController>();
  
  final List<TextEditingController> _chapterControllers = [];
  final ScrollController _editorScrollController = ScrollController();

  late Novel _currentNovel;
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  double _aiPanelWidth = 400;

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel;
    _currentChapterIndex = widget.initialChapterIndex ?? 0;
    
    _initChapterControllers();
    _initAgent();
  }

  void _initChapterControllers() {
    _chapterControllers.clear();
    for (final chapter in _currentNovel.chapters) {
      final controller = TextEditingController(text: chapter.content);
      controller.addListener(_onTextChanged);
      _chapterControllers.add(controller);
    }
  }

  void _initAgent() async {
    await _agentController.setCurrentNovel(_currentNovel);

    if (_currentNovel.chapters.isNotEmpty &&
        _currentChapterIndex < _currentNovel.chapters.length) {
      _agentController.currentChapter.value = _currentNovel.chapters[_currentChapterIndex];
    }

    // 监听章节内容变化
    _agentController.currentChapter.listen((chapter) {
      if (chapter != null && _currentChapterIndex < _chapterControllers.length) {
        // 更新编辑器内容，但不触发 _onTextChanged
        _chapterControllers[_currentChapterIndex].removeListener(_onTextChanged);
        _chapterControllers[_currentChapterIndex].text = chapter.content;
        _chapterControllers[_currentChapterIndex].addListener(_onTextChanged);
      }
    });
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('《》- 岱宗AI辅助助手'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasChanges ? _saveChanges : null,
            tooltip: '保存',
          ),
        ],
      ),
      body: Row(
        children: [
          Expanded(
            flex: 3,
            child: _buildEditor(),
          ),
          Container(
            width: 1,
            color: Theme.of(context).dividerColor,
          ),
          SizedBox(
            width: _aiPanelWidth,
            child: NovelAgentPanel(
              novel: _currentNovel,
              width: _aiPanelWidth,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          const Text('章节：'),
          const SizedBox(width: 8),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _currentNovel.chapters.asMap().entries.map((entry) {
                  final index = entry.key;
                  final chapter = entry.value;
                  final isSelected = index == _currentChapterIndex;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ChoiceChip(
                      label: Text('第章'),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          _switchToChapter(index);
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return const Center(
        child: Text('暂无章节内容'),
      );
    }

    if (_currentChapterIndex >= _chapterControllers.length) {
      return const Center(
        child: Text('章节索引超出范围'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _currentNovel.chapters[_currentChapterIndex].title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: TextField(
            controller: _chapterControllers[_currentChapterIndex],
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编辑章节内容...',
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
            ),
          ),
        ),
      ],
    );
  }

  void _switchToChapter(int index) {
    if (index >= 0 && index < _currentNovel.chapters.length) {
      setState(() {
        _currentChapterIndex = index;
      });

      // 更新 Agent 控制器的当前章节
      _agentController.currentChapter.value = _currentNovel.chapters[index];

      // 同步编辑器内容
      if (index < _chapterControllers.length) {
        _chapterControllers[index].removeListener(_onTextChanged);
        _chapterControllers[index].text = _currentNovel.chapters[index].content;
        _chapterControllers[index].addListener(_onTextChanged);
      }
    }
  }

  void _saveChanges() async {
    try {
      for (int i = 0; i < _chapterControllers.length; i++) {
        if (i < _currentNovel.chapters.length) {
          _currentNovel.chapters[i].content = _chapterControllers[i].text;
        }
      }
      
      await _novelController.updateNovel(_currentNovel);
      
      setState(() {
        _hasChanges = false;
      });
      
      Get.snackbar(
        '保存成功',
        '章节内容已保存',
        snackPosition: SnackPosition.BOTTOM,
      );
      
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存章节内容时出错: ',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    _editorScrollController.dispose();
    super.dispose();
  }
}
