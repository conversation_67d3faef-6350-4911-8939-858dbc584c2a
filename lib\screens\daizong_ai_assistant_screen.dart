﻿import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/novel_agent_controller.dart';
import '../controllers/novel_controller.dart';
import '../models/novel.dart';
import '../models/chapter_snapshot.dart';
import '../services/version_control_service.dart';
import '../widgets/novel_agent_panel.dart';

class DaizongAIAssistantScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const DaizongAIAssistantScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<DaizongAIAssistantScreen> createState() => _DaizongAIAssistantScreenState();
}

class _DaizongAIAssistantScreenState extends State<DaizongAIAssistantScreen> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  final NovelController _novelController = Get.find<NovelController>();
  final VersionControlService _versionControlService = Get.put(VersionControlService());
  
  final List<TextEditingController> _chapterControllers = [];
  final List<TextEditingController> _chapterTitleControllers = [];
  final ScrollController _editorScrollController = ScrollController();

  late Novel _currentNovel;
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  double _aiPanelWidth = 400;

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel;
    _currentChapterIndex = widget.initialChapterIndex ?? 0;
    
    _initChapterControllers();
    _initAgent();
  }

  void _initChapterControllers() {
    // 清空现有控制器
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.dispose();
    }

    _chapterControllers.clear();
    _chapterTitleControllers.clear();

    print('初始化控制器，章节数量: ${_currentNovel.chapters.length}');

    for (int i = 0; i < _currentNovel.chapters.length; i++) {
      final chapter = _currentNovel.chapters[i];

      // 内容控制器
      final contentController = TextEditingController(text: chapter.content);
      contentController.addListener(_onTextChanged);
      _chapterControllers.add(contentController);

      // 标题控制器
      final titleController = TextEditingController(text: chapter.title);
      titleController.addListener(_onTitleChanged);
      _chapterTitleControllers.add(titleController);

      print('初始化第${i}章控制器: ${chapter.title}');
    }

    print('控制器初始化完成 - 内容控制器: ${_chapterControllers.length}, 标题控制器: ${_chapterTitleControllers.length}');
  }

  void _initAgent() async {
    await _agentController.setCurrentNovel(_currentNovel);

    // 加载版本控制历史
    await _versionControlService.loadNovelHistory(_currentNovel.id);

    if (_currentNovel.chapters.isNotEmpty &&
        _currentChapterIndex < _currentNovel.chapters.length) {
      _agentController.currentChapter.value = _currentNovel.chapters[_currentChapterIndex];
    }

    // 监听章节内容变化
    _agentController.currentChapter.listen((chapter) {
      if (chapter != null && _currentChapterIndex < _chapterControllers.length) {
        // 更新编辑器内容，但不触发 _onTextChanged
        _chapterControllers[_currentChapterIndex].removeListener(_onTextChanged);
        _chapterControllers[_currentChapterIndex].text = chapter.content;
        _chapterControllers[_currentChapterIndex].addListener(_onTextChanged);
      }
    });
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
    _recordContentChange();
  }

  void _onTitleChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
    _recordTitleChange();
  }

  /// 记录内容变更
  void _recordContentChange() {
    if (_currentChapterIndex < _currentNovel.chapters.length &&
        _currentChapterIndex < _chapterControllers.length) {

      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      final newContent = _chapterControllers[_currentChapterIndex].text;

      if (currentChapter.content != newContent) {
        _versionControlService.recordChange(
          novelId: _currentNovel.id,
          chapterNumber: currentChapter.number,
          previousTitle: currentChapter.title,
          currentTitle: currentChapter.title,
          previousContent: currentChapter.content,
          currentContent: newContent,
          action: VersionControlAction.contentChange,
        );
      }
    }
  }

  /// 记录标题变更
  void _recordTitleChange() {
    if (_currentChapterIndex < _currentNovel.chapters.length &&
        _currentChapterIndex < _chapterTitleControllers.length) {

      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      final newTitle = _chapterTitleControllers[_currentChapterIndex].text;

      if (currentChapter.title != newTitle) {
        _versionControlService.recordChange(
          novelId: _currentNovel.id,
          chapterNumber: currentChapter.number,
          previousTitle: currentChapter.title,
          currentTitle: newTitle,
          previousContent: currentChapter.content,
          currentContent: currentChapter.content,
          action: VersionControlAction.titleChange,
        );
      }
    }
  }

  /// 撤销操作
  void _undoChange() {
    final lastChange = _versionControlService.undo();
    if (lastChange == null) return;

    // 找到对应的章节
    final chapterIndex = _currentNovel.chapters.indexWhere(
      (chapter) => chapter.number == lastChange.chapterNumber,
    );

    if (chapterIndex == -1) return;

    // 恢复到之前的状态
    setState(() {
      // 更新章节数据
      _currentNovel.chapters[chapterIndex] = _currentNovel.chapters[chapterIndex].copyWith(
        title: lastChange.previousTitle,
        content: lastChange.previousContent,
      );

      // 更新控制器
      if (chapterIndex < _chapterControllers.length) {
        _chapterControllers[chapterIndex].removeListener(_onTextChanged);
        _chapterControllers[chapterIndex].text = lastChange.previousContent;
        _chapterControllers[chapterIndex].addListener(_onTextChanged);
      }

      if (chapterIndex < _chapterTitleControllers.length) {
        _chapterTitleControllers[chapterIndex].removeListener(_onTitleChanged);
        _chapterTitleControllers[chapterIndex].text = lastChange.previousTitle;
        _chapterTitleControllers[chapterIndex].addListener(_onTitleChanged);
      }

      _hasChanges = true;
    });

    Get.snackbar(
      '撤销成功',
      '已恢复到上一个状态',
      duration: const Duration(seconds: 2),
    );
  }

  /// 重做操作
  void _redoChange() {
    final redoChange = _versionControlService.redo();
    if (redoChange == null) return;

    // 找到对应的章节
    final chapterIndex = _currentNovel.chapters.indexWhere(
      (chapter) => chapter.number == redoChange.chapterNumber,
    );

    if (chapterIndex == -1) return;

    // 恢复到重做的状态
    setState(() {
      // 更新章节数据
      _currentNovel.chapters[chapterIndex] = _currentNovel.chapters[chapterIndex].copyWith(
        title: redoChange.currentTitle,
        content: redoChange.currentContent,
      );

      // 更新控制器
      if (chapterIndex < _chapterControllers.length) {
        _chapterControllers[chapterIndex].removeListener(_onTextChanged);
        _chapterControllers[chapterIndex].text = redoChange.currentContent;
        _chapterControllers[chapterIndex].addListener(_onTextChanged);
      }

      if (chapterIndex < _chapterTitleControllers.length) {
        _chapterTitleControllers[chapterIndex].removeListener(_onTitleChanged);
        _chapterTitleControllers[chapterIndex].text = redoChange.currentTitle;
        _chapterTitleControllers[chapterIndex].addListener(_onTitleChanged);
      }

      _hasChanges = true;
    });

    Get.snackbar(
      '重做成功',
      '已恢复被撤销的修改',
      duration: const Duration(seconds: 2),
    );
  }

  /// 创建快照
  void _createSnapshot() {
    if (_currentChapterIndex >= _currentNovel.chapters.length) return;

    final currentChapter = _currentNovel.chapters[_currentChapterIndex];
    final TextEditingController noteController = TextEditingController();

    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.camera_alt, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '创建快照',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                '第${currentChapter.number}章：${currentChapter.title}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: noteController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: '快照备注',
                  hintText: '为这个快照添加说明...',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        final note = noteController.text.trim();
                        if (note.isEmpty) {
                          Get.snackbar('提示', '请输入快照备注');
                          return;
                        }

                        try {
                          await _versionControlService.createSnapshot(
                            novelId: _currentNovel.id,
                            chapterNumber: currentChapter.number,
                            title: _currentChapterIndex < _chapterTitleControllers.length
                                ? _chapterTitleControllers[_currentChapterIndex].text
                                : currentChapter.title,
                            content: _currentChapterIndex < _chapterControllers.length
                                ? _chapterControllers[_currentChapterIndex].text
                                : currentChapter.content,
                            userNote: note,
                          );

                          Get.back();
                          Get.snackbar(
                            '快照创建成功',
                            '已为第${currentChapter.number}章创建快照',
                            duration: const Duration(seconds: 2),
                          );
                        } catch (e) {
                          Get.snackbar('创建失败', '快照创建失败: $e');
                        }
                      },
                      child: const Text('创建快照'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('取消'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示快照历史
  void _showSnapshotHistory() {
    if (_currentChapterIndex >= _currentNovel.chapters.length) return;

    final currentChapter = _currentNovel.chapters[_currentChapterIndex];
    final snapshots = _versionControlService.getChapterSnapshots(
      _currentNovel.id,
      currentChapter.number,
    );

    Get.dialog(
      Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.history, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '第${currentChapter.number}章 快照历史',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: snapshots.isEmpty
                    ? const Center(
                        child: Text(
                          '暂无快照',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: snapshots.length,
                        itemBuilder: (context, index) {
                          final snapshot = snapshots[index];
                          return _buildSnapshotItem(snapshot);
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建快照项
  Widget _buildSnapshotItem(ChapterSnapshot snapshot) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.bookmark,
                  size: 16,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    snapshot.userNote,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                Text(
                  _formatDateTime(snapshot.createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '标题: ${snapshot.title}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
            Text(
              '内容: ${snapshot.content.length > 50 ? snapshot.content.substring(0, 50) + '...' : snapshot.content}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _restoreSnapshot(snapshot),
                  icon: const Icon(Icons.restore, size: 16),
                  label: const Text('恢复'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteSnapshot(snapshot),
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('删除'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 恢复快照
  void _restoreSnapshot(ChapterSnapshot snapshot) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认恢复'),
        content: Text('确定要恢复到快照"${snapshot.userNote}"吗？\n当前未保存的修改将会丢失。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back(); // 关闭确认对话框
              Get.back(); // 关闭快照历史对话框

              // 恢复快照内容
              _restoreFromSnapshot(snapshot);
            },
            child: const Text('确认恢复'),
          ),
        ],
      ),
    );
  }

  /// 从快照恢复内容
  void _restoreFromSnapshot(ChapterSnapshot snapshot) {
    // 找到对应的章节
    final chapterIndex = _currentNovel.chapters.indexWhere(
      (chapter) => chapter.number == snapshot.chapterNumber,
    );

    if (chapterIndex == -1) {
      Get.snackbar('恢复失败', '找不到对应的章节');
      return;
    }

    setState(() {
      // 更新章节数据
      _currentNovel.chapters[chapterIndex] = _currentNovel.chapters[chapterIndex].copyWith(
        title: snapshot.title,
        content: snapshot.content,
      );

      // 更新控制器
      if (chapterIndex < _chapterControllers.length) {
        _chapterControllers[chapterIndex].removeListener(_onTextChanged);
        _chapterControllers[chapterIndex].text = snapshot.content;
        _chapterControllers[chapterIndex].addListener(_onTextChanged);
      }

      if (chapterIndex < _chapterTitleControllers.length) {
        _chapterTitleControllers[chapterIndex].removeListener(_onTitleChanged);
        _chapterTitleControllers[chapterIndex].text = snapshot.title;
        _chapterTitleControllers[chapterIndex].addListener(_onTitleChanged);
      }

      _hasChanges = true;
    });

    Get.snackbar(
      '恢复成功',
      '已恢复到快照"${snapshot.userNote}"',
      duration: const Duration(seconds: 2),
    );
  }

  /// 删除快照
  void _deleteSnapshot(ChapterSnapshot snapshot) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除快照"${snapshot.userNote}"吗？\n此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _versionControlService.deleteSnapshot(snapshot.id);
                Get.back();
                Get.snackbar(
                  '删除成功',
                  '快照已删除',
                  duration: const Duration(seconds: 2),
                );

                // 刷新快照历史
                _showSnapshotHistory();
              } catch (e) {
                Get.snackbar('删除失败', '删除快照失败: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('确认删除'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(48), // 减少AppBar高度
        child: AppBar(
          title: Text(
            '《${_currentNovel.title}》- 岱宗AI辅助助手',
            style: const TextStyle(fontSize: 16), // 减小标题字体
          ),
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          elevation: 1, // 减少阴影
          actions: [
            // 撤销按钮
            Obx(() => IconButton(
              icon: const Icon(Icons.undo, size: 18),
              onPressed: _versionControlService.canUndo.value ? _undoChange : null,
              tooltip: '撤销',
            )),

            // 重做按钮
            Obx(() => IconButton(
              icon: const Icon(Icons.redo, size: 18),
              onPressed: _versionControlService.canRedo.value ? _redoChange : null,
              tooltip: '重做',
            )),

            // 创建快照按钮
            IconButton(
              icon: const Icon(Icons.camera_alt, size: 18),
              onPressed: _createSnapshot,
              tooltip: '创建快照',
            ),

            // 查看快照历史按钮
            IconButton(
              icon: const Icon(Icons.history, size: 18),
              onPressed: _showSnapshotHistory,
              tooltip: '快照历史',
            ),

            // 保存按钮
            IconButton(
              icon: const Icon(Icons.save, size: 18),
              onPressed: _hasChanges ? _saveChanges : null,
              tooltip: '保存',
            ),
          ],
        ),
      ),
      body: Row(
        children: [
          // 左侧编辑器区域
          Expanded(
            child: _buildEditor(),
          ),

          // 可拖拽的分隔条
          _buildResizableHandle(context),

          // 右侧AI面板区域
          Container(
            width: _aiPanelWidth,
            child: NovelAgentPanel(
              novel: _currentNovel,
              width: _aiPanelWidth,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建可拖拽的分隔条
  Widget _buildResizableHandle(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.resizeColumn,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _aiPanelWidth = (_aiPanelWidth - details.delta.dx).clamp(300.0, 600.0);
          });
        },
        child: Container(
          width: 4, // 从8减少到4，缩小分隔条宽度
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 1, // 从2减少到1，使分隔线更细
              color: Theme.of(context).dividerColor.withOpacity(0.6),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '章节：',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _currentNovel.chapters.asMap().entries.map((entry) {
                  final index = entry.key;
                  final chapter = entry.value;
                  final isSelected = index == _currentChapterIndex;

                  // 调试信息
                  print('章节调试: index=$index, number=${chapter.number}, title=${chapter.title}');

                  return Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: ChoiceChip(
                      label: Text(
                        '第${chapter.number}章',
                        style: const TextStyle(fontSize: 12),
                      ),
                      selected: isSelected,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                      onSelected: (selected) {
                        if (selected) {
                          _switchToChapter(index);
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return const Center(
        child: Text('暂无章节内容'),
      );
    }

    if (_currentChapterIndex >= _chapterControllers.length ||
        _currentChapterIndex >= _chapterTitleControllers.length) {
      return const Center(
        child: Text('章节索引超出范围'),
      );
    }

    // 添加调试信息
    print('构建编辑器内容 - 当前章节索引: $_currentChapterIndex');
    print('章节数量: ${_currentNovel.chapters.length}');
    print('内容控制器数量: ${_chapterControllers.length}');
    print('标题控制器数量: ${_chapterTitleControllers.length}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 可编辑的章节标题
        Container(
          height: 40, // 收窄高度
          child: Row(
            children: [
              Text(
                '第${_currentNovel.chapters[_currentChapterIndex].number}章：',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              Expanded(
                child: _currentChapterIndex < _chapterTitleControllers.length
                    ? TextField(
                        controller: _chapterTitleControllers[_currentChapterIndex],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintText: '章节标题',
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          isDense: true,
                        ),
                        maxLines: 1,
                      )
                    : Text(
                        _currentNovel.chapters[_currentChapterIndex].title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8), // 减少间距
        Expanded(
          child: TextField(
            controller: _chapterControllers[_currentChapterIndex],
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编辑章节内容...',
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
            ),
          ),
        ),
      ],
    );
  }

  void _switchToChapter(int index) {
    if (index >= 0 && index < _currentNovel.chapters.length) {
      setState(() {
        _currentChapterIndex = index;
      });

      // 更新 Agent 控制器的当前章节
      _agentController.currentChapter.value = _currentNovel.chapters[index];

      // 同步编辑器内容和标题
      if (index < _chapterControllers.length && index < _currentNovel.chapters.length) {
        _chapterControllers[index].removeListener(_onTextChanged);
        _chapterControllers[index].text = _currentNovel.chapters[index].content;
        _chapterControllers[index].addListener(_onTextChanged);
      }

      if (index < _chapterTitleControllers.length && index < _currentNovel.chapters.length) {
        _chapterTitleControllers[index].removeListener(_onTitleChanged);
        _chapterTitleControllers[index].text = _currentNovel.chapters[index].title;
        _chapterTitleControllers[index].addListener(_onTitleChanged);
      }
    }
  }

  void _saveChanges() async {
    try {
      // 保存章节内容和标题
      for (int i = 0; i < _chapterControllers.length; i++) {
        if (i < _currentNovel.chapters.length) {
          // 更新内容
          _currentNovel.chapters[i].content = _chapterControllers[i].text;

          // 更新标题（使用copyWith创建新实例）
          if (i < _chapterTitleControllers.length) {
            final newTitle = _chapterTitleControllers[i].text;
            if (newTitle != _currentNovel.chapters[i].title) {
              _currentNovel.chapters[i] = _currentNovel.chapters[i].copyWith(
                title: newTitle,
                content: _chapterControllers[i].text,
              );
            }
          }
        }
      }
      
      await _novelController.updateNovel(_currentNovel);
      
      setState(() {
        _hasChanges = false;
      });
      
      Get.snackbar(
        '保存成功',
        '章节内容已保存',
        snackPosition: SnackPosition.BOTTOM,
      );
      
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存章节内容时出错: ',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.dispose();
    }
    _editorScrollController.dispose();
    super.dispose();
  }
}
