// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chapter_snapshot.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ChapterSnapshotAdapter extends TypeAdapter<ChapterSnapshot> {
  @override
  final int typeId = 30;

  @override
  ChapterSnapshot read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChapterSnapshot(
      id: fields[0] as String,
      chapterNumber: fields[1] as int,
      title: fields[2] as String,
      content: fields[3] as String,
      createdAt: fields[4] as DateTime,
      userNote: fields[5] as String,
      novelId: fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ChapterSnapshot obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.chapterNumber)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.content)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.userNote)
      ..writeByte(6)
      ..write(obj.novelId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChapterSnapshotAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VersionControlHistoryAdapter extends TypeAdapter<VersionControlHistory> {
  @override
  final int typeId = 31;

  @override
  VersionControlHistory read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VersionControlHistory(
      id: fields[0] as String,
      chapterNumber: fields[1] as int,
      previousTitle: fields[2] as String,
      currentTitle: fields[3] as String,
      previousContent: fields[4] as String,
      currentContent: fields[5] as String,
      timestamp: fields[6] as DateTime,
      action: fields[7] as VersionControlAction,
      novelId: fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, VersionControlHistory obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.chapterNumber)
      ..writeByte(2)
      ..write(obj.previousTitle)
      ..writeByte(3)
      ..write(obj.currentTitle)
      ..writeByte(4)
      ..write(obj.previousContent)
      ..writeByte(5)
      ..write(obj.currentContent)
      ..writeByte(6)
      ..write(obj.timestamp)
      ..writeByte(7)
      ..write(obj.action)
      ..writeByte(8)
      ..write(obj.novelId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VersionControlHistoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VersionControlActionAdapter extends TypeAdapter<VersionControlAction> {
  @override
  final int typeId = 32;

  @override
  VersionControlAction read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VersionControlAction.contentChange;
      case 1:
        return VersionControlAction.titleChange;
      case 2:
        return VersionControlAction.snapshot;
      default:
        return VersionControlAction.contentChange;
    }
  }

  @override
  void write(BinaryWriter writer, VersionControlAction obj) {
    switch (obj) {
      case VersionControlAction.contentChange:
        writer.writeByte(0);
        break;
      case VersionControlAction.titleChange:
        writer.writeByte(1);
        break;
      case VersionControlAction.snapshot:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VersionControlActionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
