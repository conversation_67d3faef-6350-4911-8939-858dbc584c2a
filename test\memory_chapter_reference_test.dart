import 'package:flutter_test/flutter_test.dart';
import 'package:novel_app/models/novel.dart';

void main() {
  group('章节引用解析测试', () {
    late Novel testNovel;
    
    setUp(() {
      testNovel = Novel(
        title: '测试小说',
        genre: '测试',
        outline: '测试大纲',
        chapters: [
          Chapter(number: 1, title: '第一章', content: '第一章的内容'),
          Chapter(number: 2, title: '第二章', content: '第二章的内容'),
          Chapter(number: 3, title: '第三章', content: '第三章的内容'),
        ],
      );
    });

    test('解析单个章节引用 - @第1章', () {
      const text = '@第1章 请参考第一章的风格';
      final result = _parseChapterReferences(text, testNovel);
      
      expect(result['text'], equals('请参考第一章的风格'));
      expect(result['chapters'], hasLength(1));
      expect((result['chapters'] as List<Chapter>)[0].number, equals(1));
    });

    test('解析简化章节引用 - @1', () {
      const text = '@1 删除环境描写';
      final result = _parseChapterReferences(text, testNovel);
      
      expect(result['text'], equals('删除环境描写'));
      expect(result['chapters'], hasLength(1));
      expect((result['chapters'] as List<Chapter>)[0].number, equals(1));
    });

    test('解析多个章节引用', () {
      const text = '@第1章 @第3章 结合这两章的风格';
      final result = _parseChapterReferences(text, testNovel);
      
      expect(result['text'], equals('结合这两章的风格'));
      expect(result['chapters'], hasLength(2));
      expect((result['chapters'] as List<Chapter>)[0].number, equals(1));
      expect((result['chapters'] as List<Chapter>)[1].number, equals(3));
    });

    test('解析不存在的章节引用', () {
      const text = '@第10章 不存在的章节';
      final result = _parseChapterReferences(text, testNovel);
      
      expect(result['text'], equals('不存在的章节'));
      expect(result['chapters'], hasLength(0));
    });

    test('混合格式的章节引用', () {
      const text = '@1 @第2章 @3 混合格式测试';
      final result = _parseChapterReferences(text, testNovel);
      
      expect(result['text'], equals('混合格式测试'));
      expect(result['chapters'], hasLength(3));
    });

    test('无章节引用的文本', () {
      const text = '普通的文本内容';
      final result = _parseChapterReferences(text, testNovel);
      
      expect(result['text'], equals('普通的文本内容'));
      expect(result['chapters'], hasLength(0));
    });
  });

  group('Memory上下文构建测试', () {
    test('构建完整的memory上下文', () {
      final novel = Novel(
        title: '测试小说',
        genre: '玄幻',
        outline: '测试大纲',
        style: '古典',
        chapters: [
          Chapter(number: 1, title: '第一章', content: '第一章内容'),
        ],
      );
      
      final currentChapter = novel.chapters[0];
      final referencedChapters = <Chapter>[];
      
      final memoryContext = _buildMemoryContext(novel, currentChapter, referencedChapters);
      
      expect(memoryContext['novel_info']['title'], equals('测试小说'));
      expect(memoryContext['novel_info']['genre'], equals('玄幻'));
      expect(memoryContext['novel_info']['outline'], equals('测试大纲'));
      expect(memoryContext['novel_info']['style'], equals('古典'));
      
      expect(memoryContext['current_chapter']['number'], equals(1));
      expect(memoryContext['current_chapter']['title'], equals('第一章'));
      expect(memoryContext['current_chapter']['content'], equals('第一章内容'));
      
      expect(memoryContext['context_info']['total_chapters'], equals(1));
      expect(memoryContext['context_info']['current_chapter_index'], equals(1));
    });

    test('包含引用章节的memory上下文', () {
      final novel = Novel(
        title: '测试小说',
        genre: '玄幻',
        outline: '测试大纲',
        chapters: [
          Chapter(number: 1, title: '第一章', content: '第一章内容'),
          Chapter(number: 2, title: '第二章', content: '第二章内容'),
        ],
      );
      
      final currentChapter = novel.chapters[1];
      final referencedChapters = [novel.chapters[0]];
      
      final memoryContext = _buildMemoryContext(novel, currentChapter, referencedChapters);
      
      expect(memoryContext['referenced_chapters'], hasLength(1));
      expect(memoryContext['referenced_chapters'][0]['number'], equals(1));
      expect(memoryContext['referenced_chapters'][0]['title'], equals('第一章'));
      expect(memoryContext['referenced_chapters'][0]['content'], equals('第一章内容'));
    });
  });
}

// 模拟解析章节引用的函数
Map<String, dynamic> _parseChapterReferences(String text, Novel novel) {
  final referencedChapters = <Chapter>[];
  String cleanedText = text;
  
  // 匹配 @第X章 或 @X 的模式
  final chapterPattern = RegExp(r'@(?:第)?(\d+)(?:章)?');
  final matches = chapterPattern.allMatches(text);
  
  for (final match in matches) {
    final chapterNumber = int.tryParse(match.group(1) ?? '');
    if (chapterNumber != null) {
      // 查找对应的章节
      final chapterIndex = novel.chapters.indexWhere(
        (c) => c.number == chapterNumber,
      );
      
      if (chapterIndex != -1) {
        final chapter = novel.chapters[chapterIndex];
        referencedChapters.add(chapter);
      }
    }
    
    // 从文本中移除引用标记
    cleanedText = cleanedText.replaceAll(match.group(0)!, '');
  }
  
  // 清理多余的空格
  cleanedText = cleanedText.replaceAll(RegExp(r'\s+'), ' ').trim();
  
  return {
    'text': cleanedText,
    'chapters': referencedChapters,
  };
}

// 模拟构建memory上下文的函数
Map<String, dynamic> _buildMemoryContext(
  Novel novel, 
  Chapter? currentChapter, 
  List<Chapter>? referencedChapters
) {
  final memoryContext = <String, dynamic>{};
  
  // 添加小说基本信息
  memoryContext['novel_info'] = {
    'title': novel.title,
    'genre': novel.genre,
    'outline': novel.outline,
    'style': novel.style,
  };
  
  // 添加当前章节信息
  if (currentChapter != null) {
    memoryContext['current_chapter'] = {
      'number': currentChapter.number,
      'title': currentChapter.title,
      'content': currentChapter.content,
    };
  }
  
  // 添加引用的章节
  if (referencedChapters != null && referencedChapters.isNotEmpty) {
    memoryContext['referenced_chapters'] = referencedChapters.map((chapter) => {
      'number': chapter.number,
      'title': chapter.title,
      'content': chapter.content,
    }).toList();
  }
  
  // 添加上下文信息
  memoryContext['context_info'] = {
    'total_chapters': novel.chapters.length,
    'current_chapter_index': currentChapter?.number ?? 0,
    'timestamp': DateTime.now().toIso8601String(),
  };
  
  return memoryContext;
}
