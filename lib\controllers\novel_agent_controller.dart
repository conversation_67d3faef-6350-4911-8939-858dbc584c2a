import 'package:get/get.dart';
import '../models/novel.dart';
import '../services/novel_agent_service.dart';
import '../services/novel_file_manager.dart';

/// 小说 Agent 控制器
/// 实现类似 Cursor IDE 的 Agent 功能，专门用于小说创作
class NovelAgentController extends GetxController {
  final NovelAgentService _agentService = NovelAgentService();
  final NovelFileManager _fileManager = NovelFileManager();
  
  // 响应式状态
  final RxBool isAgentMode = false.obs; // false: 聊天模式, true: 创作模式
  final RxBool isLoading = false.obs;
  final RxBool showAgentPanel = false.obs;
  final RxString currentError = ''.obs;
  
  // 当前工作的小说和章节
  final Rx<Novel?> currentNovel = Rx<Novel?>(null);
  final Rx<Chapter?> currentChapter = Rx<Chapter?>(null);
  final RxList<Chapter> availableChapters = <Chapter>[].obs;
  
  // 聊天消息
  final RxList<AgentMessage> messages = <AgentMessage>[].obs;
  
  // 待处理的编辑建议
  final RxList<EditSuggestion> pendingEdits = <EditSuggestion>[].obs;
  
  // 检查点系统
  final RxList<NovelCheckpoint> checkpoints = <NovelCheckpoint>[].obs;
  
  // 引用的章节（@ 语法）
  final RxList<Chapter> referencedChapters = <Chapter>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeAgent();
  }

  void _initializeAgent() {
    // 初始化 Agent 服务
    _agentService.initialize();
  }

  /// 设置当前工作的小说
  Future<void> setCurrentNovel(Novel novel) async {
    try {
      currentNovel.value = novel;
      
      // 加载章节列表
      await _loadChapters();
      
      // 创建初始检查点
      await _createCheckpoint('初始状态');
      
      // 清空之前的消息
      messages.clear();
      pendingEdits.clear();
      
      // 添加欢迎消息
      _addWelcomeMessage();
      
    } catch (e) {
      currentError.value = '设置小说失败: $e';
    }
  }

  /// 加载章节列表
  Future<void> _loadChapters() async {
    if (currentNovel.value == null) return;
    
    try {
      final novel = currentNovel.value!;
      
      if (novel.useFileSystem && novel.folderPath != null) {
        // 从文件系统加载章节
        final chapters = await _fileManager.loadChaptersFromFileSystem(novel.folderPath!);
        availableChapters.assignAll(chapters);
      } else {
        // 从数据库加载章节
        availableChapters.assignAll(novel.chapters);
      }
      
      // 设置当前章节为第一章
      if (availableChapters.isNotEmpty) {
        currentChapter.value = availableChapters.first;
      }
      
    } catch (e) {
      currentError.value = '加载章节失败: $e';
    }
  }

  /// 切换 Agent 模式
  void toggleAgentMode() {
    isAgentMode.value = !isAgentMode.value;
    
    // 添加模式切换提示消息
    final modeText = isAgentMode.value ? '创作模式' : '聊天模式';
    _addSystemMessage('已切换到$modeText');
  }

  /// 显示/隐藏 Agent 面板
  void toggleAgentPanel() {
    showAgentPanel.value = !showAgentPanel.value;
  }

  /// 发送消息到 Agent
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || isLoading.value) return;
    
    try {
      isLoading.value = true;
      currentError.value = '';
      
      // 解析 @ 语法引用
      final parsedContent = _parseAtReferences(content);
      
      // 添加用户消息
      _addUserMessage(content);
      
      if (isAgentMode.value) {
        // 创作模式：处理编辑指令
        await _handleCreativeMode(parsedContent);
      } else {
        // 聊天模式：正常对话
        await _handleChatMode(parsedContent);
      }
      
    } catch (e) {
      currentError.value = '发送消息失败: $e';
      _addErrorMessage('处理消息时出错: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 解析 @ 语法引用
  ParsedMessage _parseAtReferences(String content) {
    final referencedChapters = <Chapter>[];
    final regex = RegExp(r'@第(\d+)章');
    final matches = regex.allMatches(content);
    
    for (final match in matches) {
      final chapterNumber = int.tryParse(match.group(1) ?? '');
      if (chapterNumber != null) {
        final chapter = availableChapters.firstWhereOrNull(
          (c) => c.number == chapterNumber,
        );
        if (chapter != null) {
          referencedChapters.add(chapter);
        }
      }
    }
    
    // 移除 @ 引用，保留纯净的指令内容
    final cleanContent = content.replaceAll(regex, '').trim();
    
    this.referencedChapters.assignAll(referencedChapters);
    
    return ParsedMessage(
      content: cleanContent,
      referencedChapters: referencedChapters,
    );
  }

  /// 处理创作模式
  Future<void> _handleCreativeMode(ParsedMessage parsedMessage) async {
    // 创建检查点
    await _createCheckpoint('编辑前状态');
    
    // 调用 Agent 服务处理创作指令
    final result = await _agentService.processCreativeInstruction(
      instruction: parsedMessage.content,
      novel: currentNovel.value!,
      currentChapter: currentChapter.value,
      referencedChapters: parsedMessage.referencedChapters,
    );
    
    if (result.success) {
      // 添加 Agent 响应消息
      _addAgentMessage(result.response);
      
      // 如果有编辑建议，添加到待处理列表
      if (result.editSuggestions.isNotEmpty) {
        pendingEdits.addAll(result.editSuggestions);
      }
    } else {
      _addErrorMessage(result.error ?? '处理创作指令失败');
    }
  }

  /// 处理聊天模式
  Future<void> _handleChatMode(ParsedMessage parsedMessage) async {
    // 调用 Agent 服务进行对话
    final result = await _agentService.processChatMessage(
      message: parsedMessage.content,
      novel: currentNovel.value!,
      currentChapter: currentChapter.value,
      referencedChapters: parsedMessage.referencedChapters,
    );
    
    if (result.success) {
      _addAgentMessage(result.response);
    } else {
      _addErrorMessage(result.error ?? '处理聊天消息失败');
    }
  }

  /// 应用编辑建议
  Future<void> applyEditSuggestion(EditSuggestion suggestion) async {
    try {
      isLoading.value = true;
      
      // 创建检查点
      await _createCheckpoint('应用编辑建议前');
      
      // 应用编辑
      final success = await _agentService.applyEdit(suggestion);
      
      if (success) {
        // 从待处理列表中移除
        pendingEdits.remove(suggestion);
        
        // 重新加载章节内容
        await _reloadCurrentChapter();
        
        _addSystemMessage('已应用编辑建议');
      } else {
        _addErrorMessage('应用编辑建议失败');
      }
      
    } catch (e) {
      _addErrorMessage('应用编辑建议时出错: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 拒绝编辑建议
  void rejectEditSuggestion(EditSuggestion suggestion) {
    pendingEdits.remove(suggestion);
    _addSystemMessage('已拒绝编辑建议');
  }

  /// 创建检查点
  Future<void> _createCheckpoint(String description) async {
    if (currentNovel.value == null) return;
    
    try {
      final checkpoint = await _agentService.createCheckpoint(
        novel: currentNovel.value!,
        description: description,
      );
      
      checkpoints.add(checkpoint);
      
      // 限制检查点数量
      if (checkpoints.length > 10) {
        checkpoints.removeAt(0);
      }
      
    } catch (e) {
      print('创建检查点失败: $e');
    }
  }

  /// 恢复到检查点
  Future<void> restoreCheckpoint(NovelCheckpoint checkpoint) async {
    try {
      isLoading.value = true;
      
      final success = await _agentService.restoreCheckpoint(checkpoint);
      
      if (success) {
        // 重新加载章节
        await _loadChapters();
        _addSystemMessage('已恢复到检查点: ${checkpoint.description}');
      } else {
        _addErrorMessage('恢复检查点失败');
      }
      
    } catch (e) {
      _addErrorMessage('恢复检查点时出错: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 重新加载当前章节
  Future<void> _reloadCurrentChapter() async {
    if (currentChapter.value == null) return;
    
    try {
      final chapterNumber = currentChapter.value!.number;
      await _loadChapters();
      
      // 重新设置当前章节
      currentChapter.value = availableChapters.firstWhereOrNull(
        (c) => c.number == chapterNumber,
      );
      
    } catch (e) {
      print('重新加载章节失败: $e');
    }
  }

  /// 添加欢迎消息
  void _addWelcomeMessage() {
    final message = AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: '您好！我是岱宗AI辅助创作助手。\n\n'
          '• 聊天模式：我可以回答问题、提供创作建议\n'
          '• 创作模式：我可以直接编辑和修改章节内容\n'
          '• 使用 @第X章 来引用其他章节\n\n'
          '当前小说：《${currentNovel.value?.title ?? '未知'}》\n'
          '可用章节：${availableChapters.length} 章',
      type: AgentMessageType.system,
      timestamp: DateTime.now(),
    );
    
    messages.add(message);
  }

  /// 添加用户消息
  void _addUserMessage(String content) {
    final message = AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.user,
      timestamp: DateTime.now(),
    );
    
    messages.add(message);
  }

  /// 添加 Agent 消息
  void _addAgentMessage(String content) {
    final message = AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.agent,
      timestamp: DateTime.now(),
    );
    
    messages.add(message);
  }

  /// 添加系统消息
  void _addSystemMessage(String content) {
    final message = AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.system,
      timestamp: DateTime.now(),
    );
    
    messages.add(message);
  }

  /// 添加错误消息
  void _addErrorMessage(String content) {
    final message = AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.error,
      timestamp: DateTime.now(),
    );
    
    messages.add(message);
  }
}

/// 解析后的消息
class ParsedMessage {
  final String content;
  final List<Chapter> referencedChapters;
  
  ParsedMessage({
    required this.content,
    required this.referencedChapters,
  });
}

/// Agent 消息类型
enum AgentMessageType {
  user,
  agent,
  system,
  error,
}

/// Agent 消息
class AgentMessage {
  final String id;
  final String content;
  final AgentMessageType type;
  final DateTime timestamp;
  final List<EditSuggestion>? editSuggestions;
  
  AgentMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    this.editSuggestions,
  });
}


