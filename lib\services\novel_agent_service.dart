import 'package:get/get.dart';
import 'dart:io';
import 'dart:convert';
import '../models/novel.dart';
import '../controllers/api_config_controller.dart';
import '../services/ai_service.dart';
import '../services/embedding_service.dart';
import '../services/novel_file_manager.dart';
import 'novel_agent_tool_system.dart';

// 前向声明，避免循环导入
class AgentResult {
  final bool success;
  final String response;
  final List<EditSuggestion> editSuggestions;
  final String? error;

  AgentResult({
    required this.success,
    required this.response,
    required this.editSuggestions,
    this.error,
  });
}

class EditSuggestion {
  final String id;
  final String description;
  final String originalText;
  final String suggestedText;
  final int startLine;
  final int endLine;
  final Chapter targetChapter;
  final EditType type;

  EditSuggestion({
    required this.id,
    required this.description,
    required this.originalText,
    required this.suggestedText,
    required this.startLine,
    required this.endLine,
    required this.targetChapter,
    required this.type,
  });
}

enum EditType {
  replace,
  insert,
  delete,
}

class NovelCheckpoint {
  final String id;
  final String description;
  final DateTime timestamp;
  final Map<String, String> chapterContents;

  NovelCheckpoint({
    required this.id,
    required this.description,
    required this.timestamp,
    required this.chapterContents,
  });
}

/// 小说 Agent 服务
/// 实现类似 Cursor IDE 的 Agent 核心功能
class NovelAgentService {
  final AIService _aiService = Get.find<AIService>();
  final ApiConfigController _apiConfig = Get.find<ApiConfigController>();
  final EmbeddingService _embeddingService = Get.find<EmbeddingService>();
  final NovelFileManager _fileManager = NovelFileManager();
  
  // Agent 工具系统
  late final NovelAgentToolSystem _toolSystem;

  void initialize() {
    _toolSystem = NovelAgentToolSystem(
      aiService: _aiService,
      embeddingService: _embeddingService,
      fileManager: _fileManager,
    );
  }

  /// 处理创作指令
  Future<AgentResult> processCreativeInstruction({
    required String instruction,
    required Novel novel,
    Chapter? currentChapter,
    List<Chapter>? referencedChapters,
  }) async {
    try {
      // 1. 分析指令意图
      final intent = await _analyzeInstruction(instruction);
      
      // 2. 收集上下文
      final context = await _gatherContext(
        novel: novel,
        currentChapter: currentChapter,
        referencedChapters: referencedChapters ?? [],
        intent: intent,
      );
      
      // 3. 制定执行计划
      final plan = await _createExecutionPlan(intent, context);
      
      // 4. 执行计划
      final result = await _executePlan(plan, context);
      
      return result;
      
    } catch (e) {
      return AgentResult(
        success: false,
        error: '处理创作指令失败: $e',
        response: '',
        editSuggestions: [],
      );
    }
  }

  /// 处理聊天消息
  Future<AgentResult> processChatMessage({
    required String message,
    required Novel novel,
    Chapter? currentChapter,
    List<Chapter>? referencedChapters,
  }) async {
    try {
      // 构建聊天上下文
      final context = await _buildChatContext(
        novel: novel,
        currentChapter: currentChapter,
        referencedChapters: referencedChapters ?? [],
      );
      
      // 构建系统提示
      final systemPrompt = _buildChatSystemPrompt(novel);
      
      // 调用 AI 服务
      final response = await _aiService.generateText(
        systemPrompt: systemPrompt,
        userPrompt: '$context\n\n用户问题：$message',
        temperature: 0.7,
        maxTokens: 2000,
      );
      
      return AgentResult(
        success: true,
        response: response,
        editSuggestions: [],
      );
      
    } catch (e) {
      return AgentResult(
        success: false,
        error: '处理聊天消息失败: $e',
        response: '',
        editSuggestions: [],
      );
    }
  }

  /// 分析指令意图
  Future<InstructionIntent> _analyzeInstruction(String instruction) async {
    // 使用轻量级模型快速分析指令意图
    final systemPrompt = '''
你是一个指令分析助手，需要分析用户的创作指令并返回结构化的意图信息。

请分析以下指令的意图类型和目标：
- 编辑类型：修改、删除、插入、替换
- 目标范围：整章、段落、句子、词语
- 具体操作：优化文字、调整情节、修改对话、增加描写等

返回JSON格式：
{
  "type": "edit|generate|analyze",
  "scope": "chapter|paragraph|sentence|word",
  "operation": "具体操作描述",
  "target": "目标内容描述"
}
''';
    
    try {
      final response = await _aiService.generateText(
        systemPrompt: systemPrompt,
        userPrompt: instruction,
        temperature: 0.1,
        maxTokens: 200,
      );
      
      // 解析 JSON 响应
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        final jsonStr = jsonMatch.group(0)!;
        final data = json.decode(jsonStr);
        
        return InstructionIntent(
          type: _parseIntentType(data['type']),
          scope: _parseIntentScope(data['scope']),
          operation: data['operation'] ?? '',
          target: data['target'] ?? '',
        );
      }
      
    } catch (e) {
      print('分析指令意图失败: $e');
    }
    
    // 默认返回编辑意图
    return InstructionIntent(
      type: IntentType.edit,
      scope: IntentScope.paragraph,
      operation: instruction,
      target: '当前章节',
    );
  }

  /// 收集上下文信息
  Future<AgentContext> _gatherContext({
    required Novel novel,
    Chapter? currentChapter,
    required List<Chapter> referencedChapters,
    required InstructionIntent intent,
  }) async {
    final context = AgentContext(
      novel: novel,
      currentChapter: currentChapter,
      referencedChapters: referencedChapters,
      intent: intent,
    );
    
    // 如果需要语义搜索相关章节
    if (intent.type == IntentType.edit && referencedChapters.isEmpty) {
      final relevantChapters = await _findRelevantChapters(
        novel: novel,
        query: intent.operation,
        currentChapter: currentChapter,
      );
      context.relevantChapters = relevantChapters;
    }
    
    return context;
  }

  /// 查找相关章节
  Future<List<Chapter>> _findRelevantChapters({
    required Novel novel,
    required String query,
    Chapter? currentChapter,
  }) async {
    try {
      // 使用嵌入服务进行语义搜索
      final searchResults = await _embeddingService.searchSimilarContent(
        query: query,
        topK: 3,
      );
      
      // 将搜索结果映射到章节
      final relevantChapters = <Chapter>[];
      for (final result in searchResults) {
        Chapter? chapter;
        try {
          chapter = novel.chapters.firstWhere(
            (c) => c.content.contains(result.content.substring(0, 100)),
          );
        } catch (e) {
          chapter = null;
        }
        if (chapter != null && chapter != currentChapter) {
          relevantChapters.add(chapter);
        }
      }
      
      return relevantChapters;
      
    } catch (e) {
      print('查找相关章节失败: $e');
      return [];
    }
  }

  /// 创建执行计划
  Future<ExecutionPlan> _createExecutionPlan(
    InstructionIntent intent,
    AgentContext context,
  ) async {
    final steps = <ExecutionStep>[];
    
    switch (intent.type) {
      case IntentType.edit:
        steps.add(ExecutionStep(
          type: StepType.analyzeContent,
          description: '分析当前章节内容',
          tool: 'content_analyzer',
        ));
        
        steps.add(ExecutionStep(
          type: StepType.generateEdit,
          description: '生成编辑建议',
          tool: 'edit_generator',
        ));
        
        break;
        
      case IntentType.generate:
        steps.add(ExecutionStep(
          type: StepType.gatherContext,
          description: '收集创作上下文',
          tool: 'context_gatherer',
        ));
        
        steps.add(ExecutionStep(
          type: StepType.generateContent,
          description: '生成新内容',
          tool: 'content_generator',
        ));
        
        break;
        
      case IntentType.analyze:
        steps.add(ExecutionStep(
          type: StepType.analyzeContent,
          description: '分析内容',
          tool: 'content_analyzer',
        ));
        
        break;
    }
    
    return ExecutionPlan(
      intent: intent,
      context: context,
      steps: steps,
    );
  }

  /// 执行计划
  Future<AgentResult> _executePlan(
    ExecutionPlan plan,
    AgentContext context,
  ) async {
    final editSuggestions = <EditSuggestion>[];
    String response = '';
    
    for (final step in plan.steps) {
      try {
        final stepResult = await _executeStep(step, context);
        
        if (stepResult.editSuggestions.isNotEmpty) {
          editSuggestions.addAll(stepResult.editSuggestions);
        }
        
        if (stepResult.response.isNotEmpty) {
          response += stepResult.response + '\n\n';
        }
        
      } catch (e) {
        return AgentResult(
          success: false,
          error: '执行步骤失败: ${step.description} - $e',
          response: '',
          editSuggestions: [],
        );
      }
    }
    
    return AgentResult(
      success: true,
      response: response.trim(),
      editSuggestions: editSuggestions,
    );
  }

  /// 执行单个步骤
  Future<StepResult> _executeStep(
    ExecutionStep step,
    AgentContext context,
  ) async {
    switch (step.tool) {
      case 'content_analyzer':
        return await _toolSystem.analyzeContent(context);
        
      case 'edit_generator':
        return await _toolSystem.generateEdit(context);
        
      case 'content_generator':
        return await _toolSystem.generateContent(context);
        
      case 'context_gatherer':
        return await _toolSystem.gatherContext(context);
        
      default:
        throw Exception('未知工具: ${step.tool}');
    }
  }

  /// 应用编辑
  Future<bool> applyEdit(EditSuggestion suggestion) async {
    try {
      final chapter = suggestion.targetChapter;
      final lines = chapter.content.split('\n');
      
      switch (suggestion.type) {
        case EditType.replace:
          // 替换指定行范围的内容
          final newLines = List<String>.from(lines);
          newLines.removeRange(suggestion.startLine, suggestion.endLine + 1);
          newLines.insertAll(suggestion.startLine, suggestion.suggestedText.split('\n'));
          
          chapter.content = newLines.join('\n');
          break;
          
        case EditType.insert:
          // 插入新内容
          final newLines = List<String>.from(lines);
          newLines.insertAll(suggestion.startLine, suggestion.suggestedText.split('\n'));
          
          chapter.content = newLines.join('\n');
          break;
          
        case EditType.delete:
          // 删除指定行范围
          final newLines = List<String>.from(lines);
          newLines.removeRange(suggestion.startLine, suggestion.endLine + 1);
          
          chapter.content = newLines.join('\n');
          break;
      }
      
      // 保存章节
      await _saveChapter(chapter);
      
      return true;
      
    } catch (e) {
      print('应用编辑失败: $e');
      return false;
    }
  }

  /// 创建检查点
  Future<NovelCheckpoint> createCheckpoint({
    required Novel novel,
    required String description,
  }) async {
    final chapterContents = <String, String>{};
    
    // 保存所有章节的当前状态
    for (final chapter in novel.chapters) {
      chapterContents[chapter.number.toString()] = chapter.content;
    }
    
    return NovelCheckpoint(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      description: description,
      timestamp: DateTime.now(),
      chapterContents: chapterContents,
    );
  }

  /// 恢复检查点
  Future<bool> restoreCheckpoint(NovelCheckpoint checkpoint) async {
    try {
      // TODO: 实现检查点恢复逻辑
      return true;
    } catch (e) {
      print('恢复检查点失败: $e');
      return false;
    }
  }

  /// 保存章节
  Future<void> _saveChapter(Chapter chapter) async {
    // TODO: 根据小说类型选择保存方式（文件系统或数据库）
  }

  /// 构建聊天上下文
  Future<String> _buildChatContext({
    required Novel novel,
    Chapter? currentChapter,
    required List<Chapter> referencedChapters,
  }) async {
    final context = StringBuffer();
    
    context.writeln('小说信息：');
    context.writeln('标题：${novel.title}');
    context.writeln('类型：${novel.genre}');
    context.writeln('大纲：${novel.outline}');
    
    if (currentChapter != null) {
      context.writeln('\n当前章节：');
      context.writeln('第${currentChapter.number}章：${currentChapter.title}');
      context.writeln('内容摘要：${currentChapter.content.substring(0, 200)}...');
    }
    
    if (referencedChapters.isNotEmpty) {
      context.writeln('\n引用章节：');
      for (final chapter in referencedChapters) {
        context.writeln('第${chapter.number}章：${chapter.title}');
        context.writeln('内容摘要：${chapter.content.substring(0, 100)}...');
      }
    }
    
    return context.toString();
  }

  /// 构建聊天系统提示
  String _buildChatSystemPrompt(Novel novel) {
    return '''
你是岱宗AI辅助创作助手，专门帮助用户进行小说创作。

当前工作模式：聊天模式
- 回答用户关于小说创作的问题
- 提供创作建议和灵感
- 分析情节和人物发展
- 不直接修改文本内容

请基于提供的小说信息和上下文，给出专业、有用的创作建议。
''';
  }

  // 辅助方法
  IntentType _parseIntentType(String? type) {
    switch (type?.toLowerCase()) {
      case 'edit': return IntentType.edit;
      case 'generate': return IntentType.generate;
      case 'analyze': return IntentType.analyze;
      default: return IntentType.edit;
    }
  }

  IntentScope _parseIntentScope(String? scope) {
    switch (scope?.toLowerCase()) {
      case 'chapter': return IntentScope.chapter;
      case 'paragraph': return IntentScope.paragraph;
      case 'sentence': return IntentScope.sentence;
      case 'word': return IntentScope.word;
      default: return IntentScope.paragraph;
    }
  }
}

/// 指令意图
class InstructionIntent {
  final IntentType type;
  final IntentScope scope;
  final String operation;
  final String target;
  
  InstructionIntent({
    required this.type,
    required this.scope,
    required this.operation,
    required this.target,
  });
}

/// 意图类型
enum IntentType {
  edit,    // 编辑现有内容
  generate, // 生成新内容
  analyze,  // 分析内容
}

/// 意图范围
enum IntentScope {
  chapter,   // 整章
  paragraph, // 段落
  sentence,  // 句子
  word,      // 词语
}

/// Agent 上下文
class AgentContext {
  final Novel novel;
  final Chapter? currentChapter;
  final List<Chapter> referencedChapters;
  final InstructionIntent intent;
  List<Chapter> relevantChapters = [];
  
  AgentContext({
    required this.novel,
    required this.currentChapter,
    required this.referencedChapters,
    required this.intent,
  });
}

/// 执行计划
class ExecutionPlan {
  final InstructionIntent intent;
  final AgentContext context;
  final List<ExecutionStep> steps;
  
  ExecutionPlan({
    required this.intent,
    required this.context,
    required this.steps,
  });
}

/// 执行步骤
class ExecutionStep {
  final StepType type;
  final String description;
  final String tool;
  
  ExecutionStep({
    required this.type,
    required this.description,
    required this.tool,
  });
}

/// 步骤类型
enum StepType {
  analyzeContent,
  generateEdit,
  generateContent,
  gatherContext,
}

/// 步骤结果
class StepResult {
  final String response;
  final List<EditSuggestion> editSuggestions;
  
  StepResult({
    required this.response,
    required this.editSuggestions,
  });
}
