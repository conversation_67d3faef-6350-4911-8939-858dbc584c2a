import 'package:hive/hive.dart';

part 'chapter_snapshot.g.dart';

/// 章节快照模型
@HiveType(typeId: 30)
class ChapterSnapshot {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final int chapterNumber;

  @HiveField(2)
  final String title;

  @HiveField(3)
  final String content;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final String userNote;

  @HiveField(6)
  final String novelId;

  ChapterSnapshot({
    required this.id,
    required this.chapterNumber,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.userNote,
    required this.novelId,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'chapterNumber': chapterNumber,
        'title': title,
        'content': content,
        'createdAt': createdAt.toIso8601String(),
        'userNote': userNote,
        'novelId': novelId,
      };

  factory ChapterSnapshot.fromJson(Map<String, dynamic> json) => ChapterSnapshot(
        id: json['id'] as String,
        chapterNumber: json['chapterNumber'] as int,
        title: json['title'] as String,
        content: json['content'] as String,
        createdAt: DateTime.parse(json['createdAt'] as String),
        userNote: json['userNote'] as String,
        novelId: json['novelId'] as String,
      );
}

/// 版本控制操作类型
enum VersionControlAction {
  contentChange,
  titleChange,
  snapshot,
}

/// 版本控制历史记录
@HiveType(typeId: 31)
class VersionControlHistory {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final int chapterNumber;

  @HiveField(2)
  final String previousTitle;

  @HiveField(3)
  final String currentTitle;

  @HiveField(4)
  final String previousContent;

  @HiveField(5)
  final String currentContent;

  @HiveField(6)
  final DateTime timestamp;

  @HiveField(7)
  final VersionControlAction action;

  @HiveField(8)
  final String novelId;

  VersionControlHistory({
    required this.id,
    required this.chapterNumber,
    required this.previousTitle,
    required this.currentTitle,
    required this.previousContent,
    required this.currentContent,
    required this.timestamp,
    required this.action,
    required this.novelId,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'chapterNumber': chapterNumber,
        'previousTitle': previousTitle,
        'currentTitle': currentTitle,
        'previousContent': previousContent,
        'currentContent': currentContent,
        'timestamp': timestamp.toIso8601String(),
        'action': action.name,
        'novelId': novelId,
      };

  factory VersionControlHistory.fromJson(Map<String, dynamic> json) => VersionControlHistory(
        id: json['id'] as String,
        chapterNumber: json['chapterNumber'] as int,
        previousTitle: json['previousTitle'] as String,
        currentTitle: json['currentTitle'] as String,
        previousContent: json['previousContent'] as String,
        currentContent: json['currentContent'] as String,
        timestamp: DateTime.parse(json['timestamp'] as String),
        action: VersionControlAction.values.firstWhere(
          (e) => e.name == json['action'],
          orElse: () => VersionControlAction.contentChange,
        ),
        novelId: json['novelId'] as String,
      );
}
