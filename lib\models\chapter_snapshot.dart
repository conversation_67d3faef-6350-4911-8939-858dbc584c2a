import 'package:hive/hive.dart';

part 'chapter_snapshot.g.dart';

/// 章节快照模型
@HiveType(typeId: 10)
class ChapterSnapshot {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final int chapterNumber;

  @HiveField(2)
  final String title;

  @HiveField(3)
  final String content;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final String description;

  @HiveField(6)
  final String novelId;

  ChapterSnapshot({
    required this.id,
    required this.chapterNumber,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.description,
    required this.novelId,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'chapterNumber': chapterNumber,
        'title': title,
        'content': content,
        'createdAt': createdAt.toIso8601String(),
        'description': description,
        'novelId': novelId,
      };

  factory ChapterSnapshot.fromJson(Map<String, dynamic> json) => ChapterSnapshot(
        id: json['id'] as String,
        chapterNumber: json['chapterNumber'] as int,
        title: json['title'] as String,
        content: json['content'] as String,
        createdAt: DateTime.parse(json['createdAt'] as String),
        description: json['description'] as String,
        novelId: json['novelId'] as String,
      );
}

/// 编辑操作历史记录
@HiveType(typeId: 11)
class EditOperation {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final int chapterNumber;

  @HiveField(2)
  final String beforeTitle;

  @HiveField(3)
  final String afterTitle;

  @HiveField(4)
  final String beforeContent;

  @HiveField(5)
  final String afterContent;

  @HiveField(6)
  final DateTime timestamp;

  @HiveField(7)
  final String operationType; // 'edit', 'title_change', 'content_change'

  EditOperation({
    required this.id,
    required this.chapterNumber,
    required this.beforeTitle,
    required this.afterTitle,
    required this.beforeContent,
    required this.afterContent,
    required this.timestamp,
    required this.operationType,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'chapterNumber': chapterNumber,
        'beforeTitle': beforeTitle,
        'afterTitle': afterTitle,
        'beforeContent': beforeContent,
        'afterContent': afterContent,
        'timestamp': timestamp.toIso8601String(),
        'operationType': operationType,
      };

  factory EditOperation.fromJson(Map<String, dynamic> json) => EditOperation(
        id: json['id'] as String,
        chapterNumber: json['chapterNumber'] as int,
        beforeTitle: json['beforeTitle'] as String,
        afterTitle: json['afterTitle'] as String,
        beforeContent: json['beforeContent'] as String,
        afterContent: json['afterContent'] as String,
        timestamp: DateTime.parse(json['timestamp'] as String),
        operationType: json['operationType'] as String,
      );
}
